"use client";

import React from "react";
import { UserProfile as UserProfileType } from "@/types/user";
import { Montserrat } from "next/font/google";
import ScrollObserver from "@/lib/scroll-observer";

// Import broken-ink components
import SocialMediaSection from "@/components/broken-ink/SocialMediaSection";
import LinksSection from "@/components/broken-ink/LinksSection";
import ServicesSection from "@/components/broken-ink/ServicesSection";
import GallerySection from "@/components/broken-ink/GallerySection";
import TeamSection from "@/components/broken-ink/TeamSection";
import FeaturesSection from "@/components/broken-ink/FeaturesSection";
import GenericSection from "@/components/broken-ink/GenericSection";

import ContactSection from "@/components/broken-ink/ContactSection";
import ReviewsSection from "@/components/broken-ink/ReviewsSection";
import HeroParallax from "@/components/broken-ink/HeroParallax";

const montserrat = Montserrat({ subsets: ["latin"] });

interface BrokenInkProfileClientProps {
  initialProfile: UserProfileType;
}

export function BrokenInkProfileClient({
  initialProfile,
}: BrokenInkProfileClientProps) {
  return (
    <ScrollObserver>
      <div
        className={`min-h-screen ${montserrat.className}`}
        style={{
          backgroundColor: "#000000",
          // backgroundColor: initialProfile.settings.colors.background,
          color: initialProfile.settings.colors.primary,
        }}
      >
        <HeroParallax profile={initialProfile} />
        <LinksSection profile={initialProfile} />
        <SocialMediaSection profile={initialProfile} />
        <FeaturesSection profile={initialProfile} />
        <ServicesSection profile={initialProfile} />
        <GenericSection profile={initialProfile} />
        <GallerySection profile={initialProfile} />
        <TeamSection profile={initialProfile} />
        <ReviewsSection profile={initialProfile} />
        <ContactSection profile={initialProfile} />
      </div>
    </ScrollObserver>
  );
}
