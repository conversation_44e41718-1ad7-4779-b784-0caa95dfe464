const fs = require('fs');
const path = require('path');
const axios = require('axios');
const sharp = require('sharp');

/**
 * Build Assets Script
 * Generates robots.txt, llm.txt, and favicon.ico for the Next.js static export
 */

const OUT_DIR = path.join(process.cwd(), 'out');
const DATA_FILE = path.join(process.cwd(), 'public', 'data', 'data.json');

/**
 * Ensure the output directory exists
 */
function ensureOutDir() {
  if (!fs.existsSync(OUT_DIR)) {
    console.log('Creating out directory...');
    fs.mkdirSync(OUT_DIR, { recursive: true });
  }
}

/**
 * Read and parse the data.json file
 */
function readDataFile() {
  try {
    const data = fs.readFileSync(DATA_FILE, 'utf8');
    return JSON.parse(data);
  } catch (error) {
    console.error('Error reading data.json:', error.message);
    throw error;
  }
}

/**
 * Generate robots.txt content
 */
function generateRobotsTxt(data) {
  const siteName = data.user?.name || 'AvencaLink';
  const siteUrl = 'https://avencalink.com'; // You can make this configurable

  return `# Robots.txt for ${siteName}
# Generated automatically during build

User-agent: *
Allow: /

# Sitemap
Sitemap: ${siteUrl}/sitemap.xml

# Crawl-delay for respectful crawling
Crawl-delay: 1

# Disallow admin or private areas (if any)
# Disallow: /admin/
# Disallow: /private/
`;
}

/**
 * Generate llm.txt content
 */
function generateLlmTxt(data) {
  const siteName = data.user?.name || 'AvencaLink';
  const bio = data.user?.bio || 'Link sharing platform';
  const description = data.settings?.pageDescription || 'Create a beautiful landing page for all your links';
  const keywords = data.settings?.pageKeywords || 'linktree, bio link, link in bio';

  return `# ${siteName} - LLM Information

## Site Description
${description}

## About
${bio}

## Business Information
Name: ${siteName}
Type: Link sharing platform / Bio link service
Keywords: ${keywords}

## Location
${data.location?.address ? `
Address: ${data.location.address.street}, ${data.location.address.city}, ${data.location.address.state}, ${data.location.address.country}
ZIP: ${data.location.address.zipCode}
Phone: ${data.location?.contact?.phone || 'Not specified'}
` : 'Location information not available'}

## Services
This is a link-in-bio platform that allows users to create a single landing page containing multiple links to their social media, websites, and other online content.

## Contact
${data.location?.contact?.whatsapp ? `WhatsApp: ${data.location.contact.whatsapp}` : ''}
${data.socialMedia?.length ? `
Social Media:
${data.socialMedia.map(social => `- ${social.text}: ${social.url}`).join('\n')}
` : ''}

## Generated
This file was automatically generated during the build process.
Last updated: ${new Date().toISOString()}
`;
}

/**
 * Download image from URL
 */
async function downloadImage(url) {
  try {
    console.log(`Downloading image from: ${url}`);
    const response = await axios.get(url, {
      responseType: 'arraybuffer',
      timeout: 30000, // 30 second timeout
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
      }
    });

    if (response.status !== 200) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    return Buffer.from(response.data);
  } catch (error) {
    console.error('Error downloading image:', error.message);
    throw error;
  }
}

/**
 * Generate favicon.ico from brand logo
 */
async function generateFavicon(brandLogoUrl) {
  try {
    console.log('Generating favicon from brand logo...');

    // Download the image
    const imageBuffer = await downloadImage(brandLogoUrl);

    // Generate multiple sizes for better compatibility
    const sizes = [16, 32, 48];
    const faviconBuffers = [];

    for (const size of sizes) {
      const buffer = await sharp(imageBuffer)
        .resize(size, size, {
          fit: 'contain',
          background: { r: 255, g: 255, b: 255, alpha: 0 } // Transparent background
        })
        .png()
        .toBuffer();
      faviconBuffers.push(buffer);
    }

    // For now, we'll use the 32x32 version as the main favicon
    // Modern browsers support PNG format with .ico extension
    const faviconPath = path.join(OUT_DIR, 'favicon.ico');
    fs.writeFileSync(faviconPath, faviconBuffers[1]); // 32x32 version

    // Also generate additional favicon formats for better compatibility
    const favicon16Path = path.join(OUT_DIR, 'favicon-16x16.png');
    const favicon32Path = path.join(OUT_DIR, 'favicon-32x32.png');

    fs.writeFileSync(favicon16Path, faviconBuffers[0]); // 16x16
    fs.writeFileSync(favicon32Path, faviconBuffers[1]); // 32x32

    console.log('✅ Favicon generated successfully (multiple formats)');
    return true;
  } catch (error) {
    console.error('❌ Error generating favicon:', error.message);
    console.log('Continuing without favicon...');
    return false;
  }
}

/**
 * Write robots.txt file
 */
function writeRobotsTxt(content) {
  try {
    const robotsPath = path.join(OUT_DIR, 'robots.txt');
    fs.writeFileSync(robotsPath, content, 'utf8');
    console.log('✅ robots.txt generated successfully');
    return true;
  } catch (error) {
    console.error('❌ Error writing robots.txt:', error.message);
    return false;
  }
}

/**
 * Write llm.txt file
 */
function writeLlmTxt(content) {
  try {
    const llmPath = path.join(OUT_DIR, 'llm.txt');
    fs.writeFileSync(llmPath, content, 'utf8');
    console.log('✅ llm.txt generated successfully');
    return true;
  } catch (error) {
    console.error('❌ Error writing llm.txt:', error.message);
    return false;
  }
}

/**
 * Main function
 */
async function main() {
  console.log('🚀 Starting build assets generation...');

  try {
    // Ensure output directory exists
    ensureOutDir();

    // Read data file
    console.log('📖 Reading data.json...');
    const data = readDataFile();

    // Generate and write robots.txt
    console.log('🤖 Generating robots.txt...');
    const robotsContent = generateRobotsTxt(data);
    writeRobotsTxt(robotsContent);

    // Generate and write llm.txt
    console.log('🧠 Generating llm.txt...');
    const llmContent = generateLlmTxt(data);
    writeLlmTxt(llmContent);

    // Generate favicon if brand logo URL exists
    if (data.user?.brandLogo) {
      console.log('🎨 Generating favicon...');
      await generateFavicon(data.user.brandLogo);
    } else {
      console.log('⚠️  No brand logo URL found, skipping favicon generation');
    }

    console.log('✅ Build assets generation completed successfully!');

  } catch (error) {
    console.error('❌ Build assets generation failed:', error.message);
    process.exit(1);
  }
}

// Run the script
if (require.main === module) {
  main();
}

module.exports = { main };
