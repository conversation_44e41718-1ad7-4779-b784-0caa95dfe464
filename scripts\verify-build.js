const fs = require('fs');
const path = require('path');

/**
 * Verify Build Script
 * Checks that all required files are present in the build output
 */

const OUT_DIR = path.join(process.cwd(), 'out');

const REQUIRED_FILES = [
  'index.html',
  'robots.txt',
  'llm.txt',
  'favicon.ico',
  'data/data.json'
];

const OPTIONAL_FILES = [
  'favicon-16x16.png',
  'favicon-32x32.png'
];

function verifyBuild() {
  console.log('🔍 Verifying build output...');

  // Check if out directory exists
  if (!fs.existsSync(OUT_DIR)) {
    console.error('❌ Out directory does not exist!');
    return false;
  }

  let allFilesPresent = true;

  // Check each required file
  for (const file of REQUIRED_FILES) {
    const filePath = path.join(OUT_DIR, file);
    if (fs.existsSync(filePath)) {
      const stats = fs.statSync(filePath);
      console.log(`✅ ${file} (${stats.size} bytes)`);
    } else {
      console.error(`❌ Missing: ${file}`);
      allFilesPresent = false;
    }
  }

  // Check optional files
  console.log('\n📋 Optional files:');
  for (const file of OPTIONAL_FILES) {
    const filePath = path.join(OUT_DIR, file);
    if (fs.existsSync(filePath)) {
      const stats = fs.statSync(filePath);
      console.log(`✅ ${file} (${stats.size} bytes)`);
    } else {
      console.log(`ℹ️  Not present: ${file}`);
    }
  }

  if (allFilesPresent) {
    console.log('\n✅ All required files are present in the build output!');
    return true;
  } else {
    console.error('\n❌ Some required files are missing from the build output!');
    return false;
  }
}

// Run verification if called directly
if (require.main === module) {
  const success = verifyBuild();
  process.exit(success ? 0 : 1);
}

module.exports = { verifyBuild };
