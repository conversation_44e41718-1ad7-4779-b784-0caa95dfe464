import { Metadata } from "next";
import { notFound } from "next/navigation";
import { BrokenInkProfileClient } from "@/components/profile-client";
import { generateMetaTitle, generateMetaDescription } from "@/lib/utils";
import {
  UserProfile,
  UserNotFoundError,
  InvalidUsernameError,
} from "@/types/user";
import { localDataService } from "@/services/localDataService";

// Fetch user profile from local data
async function getUserProfile(): Promise<UserProfile | null> {
  try {
    const profile = await localDataService.loadLocalData();
    return profile;
  } catch (error) {
    console.error("Error fetching user profile:", error);
    return null;
  }
}

export async function generateMetadata(): Promise<Metadata> {
  try {
    const profile = await getUserProfile();

    if (!profile) {
      return {
        title: "User Not Found | AvencaLink",
        description: "The requested user profile could not be found.",
      };
    }

    const title = generateMetaTitle(profile.user.username, profile.user.name);
    const description = generateMetaDescription(
      profile.user.bio,
      profile.user.username
    );
    const profileUrl = `${
      process.env.NEXT_PUBLIC_BASE_URL || "http://localhost:3001"
    }`;

    return {
      title,
      description,
      openGraph: {
        title,
        description,
        url: profileUrl,
        siteName: "AvencaLink",
        images: profile.user.avatar
          ? [
              {
                url: profile.user.avatar,
                width: 400,
                height: 400,
                alt: `${profile.user.name}'s profile picture`,
              },
            ]
          : [],
        type: "profile",
      },
      twitter: {
        card: "summary",
        title,
        description,
        images: profile.user.avatar ? [profile.user.avatar] : [],
      },
      alternates: {
        canonical: profileUrl,
      },
    };
  } catch (error) {
    console.error("Error generating metadata:", error);
    return {
      title: "Página de Links",
      description: "Create your link-in-bio page",
    };
  }
}

export default async function Home() {
  try {
    const profile = await getUserProfile();

    if (!profile) {
      notFound();
    }

    return <BrokenInkProfileClient initialProfile={profile} />;
  } catch (error) {
    console.error("Error loading user profile:", error);
    notFound();
  }
}
