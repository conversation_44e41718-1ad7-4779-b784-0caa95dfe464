# Build Process Documentation

This document describes the enhanced build process for the AvencaLink Next.js project, which automatically generates additional files required for deployment.

## Overview

The build process has been enhanced to automatically generate the following files in the `out` directory:

1. **robots.txt** - SEO-friendly robots.txt file
2. **llm.txt** - Information file for LLM crawlers
3. **favicon.ico** - Generated from the brand logo URL in data.json

## Build Scripts

### Available Commands

```bash
# Standard build (includes asset generation)
pnpm run build

# Build only Next.js (without asset generation)
pnpm run build:next

# Generate assets only (requires existing out directory)
pnpm run build:assets

# Verify build output
pnpm run build:verify

# Full build with verification
pnpm run build:full
```

### Build Process Flow

1. **Next.js Build** (`next build`)
   - Compiles the Next.js application
   - Generates static files in the `out` directory
   - Copies public files to the output

2. **Asset Generation** (`scripts/build-assets.js`)
   - Reads `public/data/data.json` for dynamic content
   - Downloads brand logo from the URL specified in `user.brandLogo`
   - Generates `favicon.ico` (32x32 PNG format with .ico extension)
   - Creates `robots.txt` with site-specific content
   - Creates `llm.txt` with business information
   - Saves all files to the `out` directory

3. **Verification** (`scripts/verify-build.js`)
   - Checks that all required files are present
   - Reports file sizes
   - Exits with error code if files are missing

## Generated Files

### robots.txt
- Generated from site data in `data.json`
- Includes site name, sitemap reference, and crawl directives
- SEO-friendly configuration allowing all crawlers

### llm.txt
- Contains structured information for LLM crawlers
- Includes business information, location, services, and contact details
- Dynamically populated from `data.json`
- Follows emerging standards for LLM-readable content

### favicon.ico
- Generated from the `user.brandLogo` URL in `data.json`
- Resized to 32x32 pixels
- Transparent background
- PNG format with .ico extension for broad compatibility

## Configuration

### Data Source
All dynamic content is sourced from `public/data/data.json`. Key fields used:

```json
{
  "user": {
    "name": "Site Name",
    "bio": "Site Description",
    "brandLogo": "https://example.com/logo.png"
  },
  "settings": {
    "pageDescription": "SEO Description",
    "pageKeywords": "keyword1, keyword2"
  },
  "location": {
    "address": { ... },
    "contact": { ... }
  },
  "socialMedia": [ ... ]
}
```

### Dependencies
The build process requires these additional dependencies:
- `sharp` - Image processing for favicon generation
- `axios` - HTTP client for downloading images

## Error Handling

The build process includes comprehensive error handling:

- **Network Issues**: If the brand logo cannot be downloaded, the build continues without generating a favicon
- **Image Processing**: If favicon generation fails, the build continues with a warning
- **File System**: If file writing fails, the build exits with an error code
- **Data Parsing**: If `data.json` is invalid, the build fails with a descriptive error

## Troubleshooting

### Common Issues

1. **Favicon Generation Fails**
   - Check that the `brandLogo` URL is accessible
   - Verify the image format is supported (PNG, JPG, WebP)
   - Ensure network connectivity

2. **Build Assets Script Fails**
   - Verify `public/data/data.json` exists and is valid JSON
   - Check that the `out` directory exists (created by Next.js build)
   - Ensure write permissions to the `out` directory

3. **Missing Files in Output**
   - Run `pnpm run build:verify` to check which files are missing
   - Ensure the build process completed successfully
   - Check console output for error messages

### Manual Asset Generation

If needed, you can run asset generation separately:

```bash
# Ensure Next.js build is complete first
pnpm run build:next

# Then generate assets
pnpm run build:assets

# Verify the output
pnpm run build:verify
```

## Development vs Production

- **Development**: Static fallback files exist in `public/` directory
- **Production**: Dynamic files are generated in `out/` directory during build
- The build process overwrites the static files with dynamic content

## Deployment

After running `pnpm run build`, the `out` directory contains all files needed for static hosting:
- All Next.js generated files
- `robots.txt` with site-specific content
- `llm.txt` with business information
- `favicon.ico` generated from brand logo
- All assets and data files

Simply upload the contents of the `out` directory to your static hosting provider.
