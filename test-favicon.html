<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Favicon Test - AvencaLink</title>
    
    <!-- Favicon links for testing -->
    <link rel="icon" type="image/x-icon" href="out/favicon.ico">
    <link rel="icon" type="image/png" sizes="16x16" href="out/favicon-16x16.png">
    <link rel="icon" type="image/png" sizes="32x32" href="out/favicon-32x32.png">
    
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .file-info {
            background: #f5f5f5;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .success {
            color: #28a745;
        }
        .favicon-preview {
            display: inline-block;
            margin: 10px;
            text-align: center;
        }
        .favicon-preview img {
            border: 1px solid #ddd;
            padding: 10px;
            background: white;
        }
    </style>
</head>
<body>
    <h1>AvencaLink Build Test</h1>
    
    <p>This page tests the generated favicon and provides information about the build output.</p>
    
    <h2>Generated Files</h2>
    
    <div class="file-info">
        <h3>✅ robots.txt</h3>
        <p>SEO-friendly robots.txt file generated from site data.</p>
        <a href="out/robots.txt" target="_blank">View robots.txt</a>
    </div>
    
    <div class="file-info">
        <h3>✅ llm.txt</h3>
        <p>Information file for LLM crawlers with business details.</p>
        <a href="out/llm.txt" target="_blank">View llm.txt</a>
    </div>
    
    <div class="file-info">
        <h3>✅ favicon.ico</h3>
        <p>Main favicon generated from brand logo URL.</p>
        <div class="favicon-preview">
            <img src="out/favicon.ico" alt="Favicon ICO" width="32" height="32">
            <br>favicon.ico (32x32)
        </div>
    </div>
    
    <div class="file-info">
        <h3>✅ Additional Favicon Formats</h3>
        <p>Multiple sizes for better browser compatibility.</p>
        <div class="favicon-preview">
            <img src="out/favicon-16x16.png" alt="Favicon 16x16" width="16" height="16">
            <br>16x16 PNG
        </div>
        <div class="favicon-preview">
            <img src="out/favicon-32x32.png" alt="Favicon 32x32" width="32" height="32">
            <br>32x32 PNG
        </div>
    </div>
    
    <h2>Build Commands</h2>
    <div class="file-info">
        <h3>Available Commands:</h3>
        <ul>
            <li><code>pnpm run build</code> - Standard build with asset generation</li>
            <li><code>pnpm run build:full</code> - Build with verification</li>
            <li><code>pnpm run build:verify</code> - Verify build output</li>
            <li><code>pnpm run build:assets</code> - Generate assets only</li>
        </ul>
    </div>
    
    <h2>Deployment Ready</h2>
    <p class="success">
        ✅ The <code>out</code> directory now contains all required files for static hosting deployment.
    </p>
    
    <p>
        <strong>Note:</strong> Check your browser's favicon (in the tab) to see if the generated favicon is working correctly.
    </p>
</body>
</html>
